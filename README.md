# ERP System - Csquare Technologies Internship Assignment

A comprehensive Enterprise Resource Planning (ERP) system built with PHP and MySQL for managing customers, items, and generating detailed reports.

## 🚀 Quick Start

### ⚡ Automated Setup (Recommended)
1. **Download/Clone** the project
2. **Run setup script**: Open `setup_database.php` in your browser
3. **Verify system**: Open `final_verification.php` to confirm everything works
4. **Start using**: Access the dashboard at `index.php`

### 🖥️ Development Server
- **Windows**: Double-click `start_server.bat`
- **Linux/Mac**: Run `./start_server.sh`
- **Manual**: `php -S localhost:8000`

## ✨ Features

### Customer Management (Task 1)
- ✅ Complete CRUD operations (Create, Read, Update, Delete)
- ✅ Form validation for all required fields
- ✅ Fields: Title, First Name, Middle Name, Last Name, Contact Number, District
- ✅ Advanced search and filtering
- ✅ Export to CSV functionality
- ✅ Responsive design for all devices

### Item Management (Task 2)
- ✅ Complete inventory management system
- ✅ Fields: Item Code, Name, Category, Sub Category, Quantity, Unit Price
- ✅ Dynamic category and subcategory dropdowns
- ✅ Stock level monitoring with visual indicators
- ✅ Low stock alerts (< 10 items)
- ✅ Search and filter by category
- ✅ Export and print functionality

### Reports & Analytics (Task 3)
- ✅ **Invoice Report**: Date range filtering, customer-specific reports
- ✅ **Invoice Item Report**: Detailed item-wise breakdown
- ✅ **Item Inventory Report**: Complete stock overview with statistics
- ✅ **Export Options**: CSV and PDF formats
- ✅ **Print Functionality**: Browser-based printing
- ✅ **Interactive Dashboard**: Real-time statistics and charts

## 🛠️ Technology Stack

- **Backend**: PHP 7.4+ (Compatible with PHP 8.x)
- **Database**: MySQL 5.7+ / MariaDB 10.3+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5.1.3 (Responsive Design)
- **Icons**: Font Awesome 6.0
- **Libraries**: jQuery 3.6.0
- **PDF Generation**: Custom PDFGenerator class
- **Security**: Prepared statements, input sanitization

## 📋 System Requirements

- **PHP**: 7.4 or higher (Recommended: 8.0+)
- **MySQL**: 5.7 or higher / MariaDB 10.3+
- **Web Server**: Apache/Nginx or PHP built-in server
- **Browser**: Chrome, Firefox, Safari, Edge (Modern browsers)
- **Memory**: 256MB PHP memory limit (for large reports)

## 🚀 Installation Options

### Option 1: XAMPP/WAMP/MAMP (Easiest)
1. **Install XAMPP**: Download from https://www.apachefriends.org/
2. **Copy files**: Place project in `htdocs/erp-system/`
3. **Start services**: Apache + MySQL
4. **Setup database**: Visit `http://localhost/erp-system/setup_database.php`
5. **Access app**: `http://localhost/erp-system/`

### Option 2: PHP Built-in Server (Quick Testing)
1. **Navigate to project**: `cd /path/to/erp-system`
2. **Start server**: `php -S localhost:8000`
3. **Setup database**: Use phpMyAdmin or command line
4. **Access app**: `http://localhost:8000`

### Option 3: Production Server
1. **Upload files** to web server
2. **Configure database** credentials in `config/database.php`
3. **Set permissions**: `chmod -R 755 /path/to/erp-system`
4. **Import database**: `mysql -u user -p assignment < database/assignment.sql`

## 🔧 Automated Setup Scripts

### Setup & Verification Tools
- **`setup_database.php`** - Automated database setup wizard
- **`test_system.php`** - Component testing and diagnostics
- **`final_verification.php`** - Comprehensive system verification
- **`start_server.bat`** - Windows development server launcher
- **`start_server.sh`** - Linux/Mac development server launcher

### Quick Setup Process
1. **Run**: `setup_database.php` (Creates database and imports schema)
2. **Test**: `test_system.php` (Verifies all components work)
3. **Verify**: `final_verification.php` (Final system check)
4. **Use**: `index.php` (Main application dashboard)

## 📁 Project Structure

```
erp-system/
├── 🔧 Setup & Configuration
│   ├── config/database.php          # Database configuration
│   ├── setup_database.php           # Automated database setup
│   ├── test_system.php             # System testing script
│   ├── final_verification.php      # Final verification
│   ├── start_server.bat            # Windows server launcher
│   ├── start_server.sh             # Linux/Mac server launcher
│   ├── .htaccess                   # Apache configuration
│   └── INSTALLATION.md             # Detailed setup guide
│
├── 🏗️ Core Application
│   ├── index.php                   # Main dashboard
│   ├── classes/
│   │   ├── Customer.php            # Customer management
│   │   ├── Item.php               # Item management
│   │   ├── Report.php             # Report generation
│   │   └── PDFGenerator.php       # PDF export functionality
│   └── includes/
│       ├── header.php             # Common header
│       └── footer.php             # Common footer
│
├── 👥 Customer Management
│   ├── customer/index.php          # Customer listing
│   ├── customer/add.php            # Add new customer
│   ├── customer/edit.php           # Edit customer
│   ├── customer/view.php           # View customer details
│   └── customer/delete.php         # Delete customer
│
├── 📦 Item Management
│   ├── item/index.php              # Item inventory listing
│   ├── item/add.php                # Add new item
│   ├── item/edit.php               # Edit item
│   ├── item/view.php               # View item details
│   └── item/delete.php             # Delete item
│
├── 📊 Reports & Analytics
│   ├── reports/invoice_report.php      # Invoice reports
│   ├── reports/invoice_item_report.php # Invoice item breakdown
│   ├── reports/item_report.php         # Inventory reports
│   ├── reports/export_*.php            # Export functionality
│   └── reports/                        # Additional report files
│
├── 🎨 Assets & Resources
│   ├── assets/css/style.css        # Custom styling (1500+ lines)
│   ├── assets/js/script.js         # Main JavaScript
│   ├── assets/js/validation.js     # Form validation
│   ├── assets/js/navbar.js         # Navigation functionality
│   └── vendor/TCPDF-main/          # PDF library (extracted)
│
└── 🗄️ Database
    └── database/assignment.sql     # Complete database schema
```

## 🎯 Key Features & Functionality

### 📊 Modern Dashboard
- **Real-time Statistics**: Customers, items, invoices, total revenue
- **Quick Actions**: Direct access to all major functions
- **Recent Activity**: Latest customers and items added
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Visual Indicators**: Color-coded status and alerts

### 👥 Advanced Customer Management
- **Complete CRUD**: Create, Read, Update, Delete operations
- **Smart Search**: Real-time filtering and search
- **Data Validation**: Sri Lankan mobile number format (10 digits)
- **District Integration**: Dropdown with all Sri Lankan districts
- **Export Options**: CSV download and print functionality
- **Relationship Tracking**: View customer invoice history

### 📦 Intelligent Item Management
- **Inventory Control**: Full stock management system
- **Category System**: Hierarchical category and subcategory structure
- **Stock Monitoring**: Visual indicators for stock levels
  - 🟢 Good Stock (10+ items)
  - 🟡 Low Stock (1-9 items)
  - 🔴 Out of Stock (0 items)
- **Price Management**: Unit price tracking and total value calculation
- **Search & Filter**: By category, name, or stock status
- **Bulk Operations**: Export and print inventory reports

### 📈 Comprehensive Reporting System
- **Invoice Reports**:
  - Date range filtering
  - Customer-specific reports
  - Revenue analysis
- **Invoice Item Reports**:
  - Detailed item breakdown
  - Quantity and pricing analysis
- **Item Inventory Reports**:
  - Complete stock overview
  - Category-wise analysis
  - Stock level summaries
- **Export Formats**: CSV and PDF with professional styling
- **Print Optimization**: Browser-friendly print layouts

## 🎯 Assumptions Made

### Database Assumptions
1. **Foreign Key Relationships**: 
   - Customer district references district.id
   - Item category references item_category.id
   - Item subcategory references item_subcategory.id
   - Invoice customer references customer.id
   - Invoice_master item_id references item.id

2. **Data Types**:
   - Contact numbers are stored as VARCHAR(10) for Sri Lankan mobile numbers
   - Prices are stored as VARCHAR but validated as DECIMAL for calculations
   - Quantities are stored as VARCHAR but validated as integers

### Business Logic Assumptions
1. **Contact Number Format**: Sri Lankan mobile numbers (10 digits starting with 0)
2. **Currency**: All prices are in Sri Lankan Rupees (LKR)
3. **Stock Management**: Items with quantity < 10 are considered "low stock"
4. **Deletion Rules**: 
   - Customers with existing invoices cannot be deleted
   - Items with invoice entries cannot be deleted

### UI/UX Assumptions
1. **Responsive Design**: Optimized for desktop, tablet, and mobile devices
2. **Browser Compatibility**: Modern browsers (Chrome, Firefox, Safari, Edge)
3. **User Experience**: Intuitive navigation with breadcrumbs and clear action buttons
4. **Data Validation**: Client-side and server-side validation for all forms

### Security Assumptions
1. **Input Sanitization**: All user inputs are sanitized using htmlspecialchars()
2. **SQL Injection Prevention**: Prepared statements used for all database queries
3. **Session Management**: Basic session handling for success/error messages

## 🐛 Known Limitations

1. **Authentication**: No user login system implemented (as not required in assignment)
2. **File Uploads**: No image upload functionality for items/customers
3. **Advanced Reporting**: No graphical charts (only statistical summaries)
4. **Email Notifications**: No email alerts for low stock items
5. **Audit Trail**: No logging of user actions and changes

## 🔧 Troubleshooting & Support

### 🚨 Common Issues & Solutions

#### Database Connection Problems
```
❌ Error: Connection failed: Access denied for user 'root'@'localhost'
```
**Solutions:**
1. Run `setup_database.php` for automated setup
2. Check MySQL service is running
3. Verify credentials in `config/database.php`
4. For XAMPP: username=`root`, password=`` (empty)

#### File Permission Issues
```
❌ Error: Permission denied
```
**Solutions:**
```bash
# Linux/Mac
chmod -R 755 /path/to/erp-system
chown -R www-data:www-data /path/to/erp-system

# Windows: Run as Administrator
```

#### Assets Not Loading (CSS/JS)
```
❌ 404 errors for assets/css/style.css
```
**Solutions:**
1. Verify `.htaccess` file exists (Apache)
2. Check file paths in `includes/header.php`
3. Clear browser cache (Ctrl+F5)
4. Ensure web server has read permissions

#### PDF Export Issues
```
❌ Error: Class 'PDFGenerator' not found
```
**Solutions:**
1. Verify `classes/PDFGenerator.php` exists
2. Use browser print function (Ctrl+P → Save as PDF)
3. Check file permissions

### 🛠️ Diagnostic Tools

1. **System Test**: `test_system.php` - Tests all components
2. **Final Verification**: `final_verification.php` - Comprehensive check
3. **Database Setup**: `setup_database.php` - Automated database setup

### 📞 Support & Contact

**For Technical Issues:**
- **Primary**: <EMAIL>
- **CC**: <EMAIL>, <EMAIL>

**Quick Help:**
- Check `INSTALLATION.md` for detailed setup guide
- Run diagnostic scripts for automated troubleshooting
- Verify system requirements are met

## 🏆 Project Status

### ✅ Completed Features
- [x] Customer Management (Full CRUD)
- [x] Item Inventory Management
- [x] Invoice Reporting System
- [x] CSV/PDF Export Functionality
- [x] Responsive Web Design
- [x] Database Integration
- [x] Search & Filter Capabilities
- [x] Automated Setup Scripts
- [x] Comprehensive Testing Suite

### 📊 System Statistics
- **Total Files**: 50+ PHP, CSS, JS files
- **Database Tables**: 7 tables with sample data
- **Code Lines**: 5000+ lines of code
- **Features**: 15+ major features implemented
- **Browser Support**: All modern browsers
- **Mobile Responsive**: Yes

## 📄 License & Credits

**Project**: ERP System - Csquare Technologies Internship Assignment
**Version**: 1.0.0
**Status**: Production Ready
**Last Updated**: January 2024

**Developed for**: Csquare Technologies
**Assignment**: Full-Stack PHP Development Internship

---

### 🎉 Ready to Use!

Your ERP system is now **fully functional** and ready for production use.

**Next Steps:**
1. 🚀 **Start**: Run `final_verification.php` to confirm everything works
2. 📊 **Explore**: Access the dashboard and test all features
3. 📈 **Customize**: Modify as needed for your specific requirements
4. 🔒 **Secure**: Implement additional security measures for production

**Happy coding!** 🎯
