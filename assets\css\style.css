/* Custom CSS for ERP System */

/* General Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Layout Structure */
.main-content {
    margin-left: 280px; /* Always open by default */
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

.main-content.sidebar-closed {
    margin-left: 0;
}

.content-wrapper {
    padding: 20px;
    min-height: 100vh;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* Modern Dashboard Styles */
.dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.dashboard-title {
    position: relative;
    z-index: 2;
}

.dashboard-icon {
    display: inline-block;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
}

.date-card {
    background: rgba(255, 255, 255, 0.15);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1rem 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.date-card i {
    margin-right: 0.5rem;
}

/* Modern Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.stat-card-body {
    padding: 2rem;
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    flex-shrink: 0;
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, #11998e, #38ef7d);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-card-danger .stat-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-content {
    flex: 1;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    line-height: 1;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    color: #718096;
    font-weight: 500;
    margin-bottom: 0;
}

.stat-card-footer {
    padding: 1rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.stat-link {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #4a5568;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.stat-link:hover {
    color: #2d3748;
}

/* Modern Quick Actions Section */
.quick-actions-section {
    margin-bottom: 3rem;
}

.section-header {
    text-align: center;
    margin-bottom: 2rem;
}

.section-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.section-title i {
    color: #667eea;
}

.section-subtitle {
    color: #718096;
    font-size: 1.1rem;
    margin: 0;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.action-card:hover::before {
    transform: scaleX(1);
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    text-decoration: none;
    color: inherit;
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.action-card-primary .action-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.action-card-success .action-icon {
    background: linear-gradient(135deg, #11998e, #38ef7d);
}

.action-card-warning .action-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.action-card-info .action-icon {
    background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.action-content {
    flex: 1;
}

.action-content h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.action-content p {
    color: #718096;
    margin: 0;
    font-size: 0.95rem;
}

.action-arrow {
    color: #cbd5e0;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.action-card:hover .action-arrow {
    color: #667eea;
    transform: translateX(5px);
}

/* Modern Recent Data Section */
.recent-data-section {
    margin-bottom: 3rem;
}

.data-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.data-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
}

.data-card-header {
    padding: 2rem 2rem 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f1f5f9;
}

.data-card-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.data-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.data-icon-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.data-icon-success {
    background: linear-gradient(135deg, #11998e, #38ef7d);
}

.data-card-title h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.data-card-title p {
    color: #718096;
    margin: 0;
    font-size: 0.9rem;
}

.view-all-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.view-all-btn:hover {
    color: #5a67d8;
    transform: translateX(3px);
}

.data-card-body {
    padding: 1rem 2rem 2rem;
}

.data-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.data-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 12px;
    transition: all 0.2s ease;
}

.data-item:hover {
    background: #edf2f7;
    transform: translateX(5px);
}

.data-item-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #e2e8f0, #cbd5e0);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4a5568;
    font-size: 1rem;
    flex-shrink: 0;
}

.data-item-content {
    flex: 1;
}

.data-item-name {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.data-item-details {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.data-detail {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #718096;
    font-size: 0.85rem;
}

.data-detail i {
    font-size: 0.75rem;
    width: 12px;
}

.data-detail.price {
    color: #38a169;
    font-weight: 600;
}

.data-item-action {
    flex-shrink: 0;
}

.action-btn {
    width: 32px;
    height: 32px;
    background: #667eea;
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #5a67d8;
    color: white;
    transform: scale(1.1);
}

.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #a0aec0;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state p {
    margin: 0;
    font-size: 1.1rem;
}

/* Modern Alert Styles */
.alert-modern {
    border-radius: 15px;
    border: none;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    box-shadow: 0 4px 20px rgba(220, 53, 69, 0.15);
    margin-bottom: 2rem;
}

.alert-modern i {
    font-size: 1.2rem;
}

/* Additional Modern Utilities */
.text-gradient {
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-modern {
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.shadow-modern-hover:hover {
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
    transform: translateY(-1px);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border: none;
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    transform: translateY(-1px);
}

/* Form Styles */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

/* Table Styles */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
}

.table td {
    vertical-align: middle;
    border-color: #e9ecef;
}

/* Mobile toggle button (hidden by default) */
.sidebar-toggle.mobile-only {
    position: fixed;
    top: 20px;
    left: 20px;
    background: #1976d2;
    border: none;
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 10px;
    display: none;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    z-index: 1002;
}

.sidebar-toggle.mobile-only:hover {
    background: #1565c0;
    transform: scale(1.05);
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    left: 0; /* Always visible by default */
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    transition: left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    overflow-x: hidden;
    border-right: 1px solid rgba(0, 0, 0, 0.1);
}

.sidebar.closed {
    left: -280px;
}

/* Custom Scrollbar for Sidebar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.sidebar.active {
    left: 0;
    animation: slideInLeft 0.3s ease-out;
}

/* Sidebar Animations */
@keyframes slideInLeft {
    from {
        left: -280px;
        opacity: 0.8;
    }
    to {
        left: 0;
        opacity: 1;
    }
}

.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.3);
}

.company-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.company-logo {
    width: 40px;
    height: 40px;
    background: #2196f3;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
}

.company-info {
    flex: 1;
}

.company-name {
    color: #1976d2;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-close {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.sidebar-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Sidebar Menu */
.sidebar-menu {
    padding: 20px 0;
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 2px;
}

.nav-link {
    display: flex !important;
    align-items: center;
    flex-wrap: nowrap;
    padding: 12px 20px;
    color: #424242;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
    border-radius: 0;
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
    margin: 0;
    min-height: 48px;
    box-sizing: border-box;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.nav-link:hover {
    color: #1976d2;
    background: rgba(33, 150, 243, 0.1);
}

/* Active navigation states */
.nav-link.active {
    color: #1976d2 !important;
    background: rgba(33, 150, 243, 0.15) !important;
    font-weight: 600;
}

.nav-link.active .nav-icon {
    color: #1976d2 !important;
}

.nav-icon {
    width: 20px;
    height: 20px;
    min-width: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 16px;
    color: #666;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.nav-link:hover .nav-icon {
    color: #1976d2;
}

.nav-text {
    font-size: 14px;
    font-weight: 500;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Remove submenu and arrow styles for simple flat navigation */

/* Sidebar Footer - Removed admin info section */



/* User Menu */
.user-menu .nav-link-content {
    gap: 10px;
}

.user-avatar {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #ff9a9e, #fecfef);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 0.9rem;
    box-shadow: 0 2px 10px rgba(255, 154, 158, 0.3);
}

/* Modern Dropdown */
.modern-dropdown {
    background: white;
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    margin-top: 8px;
    min-width: 220px;
}

.modern-dropdown-item {
    padding: 12px 20px;
    color: #333;
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: 0;
    display: flex;
    align-items: center;
}

.modern-dropdown-item:hover {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #007bff;
    transform: translateX(5px);
}

.modern-dropdown-item.text-danger:hover {
    background: linear-gradient(135deg, #ffe6e6, #ffcccc);
    color: #dc3545;
}

.dropdown-divider {
    margin: 8px 16px;
    border-color: #e9ecef;
}

/* Active Navigation States */
.modern-nav-link.active {
    background: rgba(255, 255, 255, 0.15);
    color: white !important;
}

.modern-nav-link.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #ee5a24);
    border-radius: 2px;
}

/* Navbar Animations */
@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modern-navbar {
    animation: slideInDown 0.5s ease-out;
}

/* Dropdown Animation */
.modern-dropdown {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* STOP NAVBAR ANIMATIONS BUT KEEP NAVBAR VISIBLE */
.nav-loading,
.nav-loading::before,
.nav-loading::after {
    display: none !important;
    animation: none !important;
    transition: none !important;
}

/* Sidebar always visible by default */
.sidebar {
    display: block;
    opacity: 1;
    visibility: visible;
    position: fixed;
    left: 0;
    top: 0;
    width: 280px;
    height: 100vh;
    z-index: 1000;
}

/* Kill all navbar keyframes */

/* Instant feedback for navigation clicks */
.nav-link:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* Prevent layout shifts during loading */
.nav-link,
.nav-link * {
    box-sizing: border-box;
}

.nav-link {
    transition: all 0.3s ease;
}

/* Ensure consistent layout during all states */
.nav-link:hover,
.nav-link:focus,
.nav-link:active,
.nav-link.loading,
.nav-link:visited {
    display: flex !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
}

/* Force stable layout for all nav elements */
.nav-link,
.nav-link:before,
.nav-link:after {
    box-sizing: border-box !important;
}

/* Prevent any text wrapping or layout shifts */
.nav-text {
    display: inline-block;
    vertical-align: middle;
}

.nav-icon {
    display: inline-flex !important;
    vertical-align: middle;
}

/* Notification Badge */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
}

/* Breadcrumb Enhancement */
.breadcrumb-container {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 15px 0;
    margin-bottom: 20px;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.breadcrumb {
    margin-bottom: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #007bff;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
    transition: all 0.3s ease;
    border: none;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.scroll-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
    background: linear-gradient(135deg, #0056b3, #004085);
}

/* Enhanced Mobile Toggle */
.modern-toggler.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.modern-toggler.active span:nth-child(2) {
    opacity: 0;
}

.modern-toggler.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Navbar Transition */
.modern-navbar {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Enhanced Dropdown Hover Effects */
.modern-dropdown-item {
    position: relative;
    overflow: hidden;
}

.modern-dropdown-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 123, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.modern-dropdown-item:hover::before {
    left: 100%;
}

/* Alert Styles */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    color: #721c24;
}

/* Search and Filter Styles */
.search-box {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

/* Pagination Styles */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #007bff;
    font-weight: 500;
}

.pagination .page-link:hover {
    background-color: #007bff;
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

/* Loading Spinner */
.loading {
    display: none;
    text-align: center;
    padding: 2rem;
}

.spinner-border {
    color: #007bff;
}

/* Modern Responsive Design */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .dashboard-header .row {
        flex-direction: column;
        text-align: center;
    }

    .dashboard-title h1 {
        font-size: 2rem;
    }

    .dashboard-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .date-card {
        margin-top: 1rem;
        display: inline-block;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card-body {
        padding: 1.5rem;
        gap: 1rem;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .quick-actions-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .action-card {
        padding: 1.5rem;
        gap: 1rem;
    }

    .action-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .data-card-header {
        padding: 1.5rem 1.5rem 1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .data-card-body {
        padding: 1rem 1.5rem 1.5rem;
    }

    .data-item {
        padding: 0.75rem;
        gap: 0.75rem;
    }

    .data-item-details {
        flex-direction: column;
        gap: 0.5rem;
    }

    .data-item-avatar {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .table-responsive {
        border-radius: 10px;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    /* Mobile Sidebar */
    .main-content {
        margin-left: 0 !important;
    }

    /* Show mobile toggle button */
    .sidebar-toggle.mobile-only {
        display: flex !important;
    }

    .sidebar {
        width: 100%;
        left: -100%;
    }

    .sidebar.active {
        left: 0;
    }

    .header-brand .brand-text {
        font-size: 16px;
    }

    .sidebar-header {
        padding: 20px 15px;
    }

    .sidebar-brand .brand-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .sidebar-brand .brand-info h4 {
        font-size: 16px;
    }

    .nav-link {
        padding: 12px 15px;
    }

    .nav-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .nav-text {
        font-size: 14px;
    }

    .submenu-link {
        padding: 10px 15px 10px 50px;
        font-size: 13px;
    }

    .sidebar-footer {
        padding: 15px;
    }

    .user-avatar-large {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .user-details h5 {
        font-size: 13px;
    }

    .user-details p {
        font-size: 11px;
    }
}

@media (max-width: 576px) {
    .content-wrapper {
        padding: 15px;
    }



    .sidebar {
        width: 100%;
    }

    .sidebar-header {
        padding: 15px;
    }

    .nav-link {
        padding: 10px 15px;
    }

    .nav-icon {
        width: 35px;
        height: 35px;
        font-size: 14px;
        margin-right: 10px;
    }

    .nav-text {
        font-size: 13px;
    }
}

/* Desktop Enhancements */
@media (min-width: 769px) {
    .sidebar:hover {
        box-shadow: 4px 0 30px rgba(0, 0, 0, 0.15);
    }

    .main-content.sidebar-open {
        margin-left: 280px;
    }

    .sidebar-overlay {
        display: none;
    }
}

/* Print Styles */
@media print {
    .navbar, .btn, .pagination {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
    
    body {
        background: white;
    }
}

/* Custom Validation Styles */
.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

.is-valid {
    border-color: #28a745 !important;
}

.valid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #28a745;
    margin-top: 0.25rem;
}
