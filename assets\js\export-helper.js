/**
 * Enhanced Export Helper
 * Provides user-friendly export functionality with progress indicators
 */

class ExportHelper {
    constructor() {
        this.exportInProgress = false;
        this.init();
    }

    init() {
        // Bind export link handlers
        $(document).on('click', '.export-link', (e) => this.handleExportClick(e));
        $(document).on('click', '.quick-export', (e) => this.handleQuickExport(e));
        
        // Bind copy functionality
        window.copyTableData = () => this.copyTableData();
        
        // Bind print functionality
        $(document).on('click', '.print-btn', (e) => this.handlePrint(e));
        
        console.log('Export Helper initialized');
    }

    handleExportClick(e) {
        if (this.exportInProgress) {
            e.preventDefault();
            return false;
        }

        const link = e.currentTarget;
        const format = $(link).data('format') || 'csv';
        
        this.startExport(link, format);
        e.preventDefault();
        return false;
    }

    handleQuickExport(e) {
        if (this.exportInProgress) {
            e.preventDefault();
            return false;
        }

        const link = e.currentTarget;
        const format = $(link).data('format') || 'csv';
        
        this.startExport(link, format, true);
        e.preventDefault();
        return false;
    }

    startExport(link, format, isQuick = false) {
        this.exportInProgress = true;
        
        // Show loading state
        const originalText = $(link).html();
        const loadingText = isQuick ? 
            '<i class="fas fa-spinner fa-spin"></i>' : 
            '<i class="fas fa-spinner fa-spin"></i> Generating...';
        
        $(link).html(loadingText);
        $(link).addClass('disabled');
        
        // Show progress notification
        const progressNotification = this.showProgressNotification(format);
        
        // Create hidden iframe for download
        const iframe = $('<iframe>').hide().appendTo('body');
        iframe.attr('src', $(link).attr('href'));
        
        // Handle completion
        setTimeout(() => {
            this.completeExport(link, originalText, format, iframe, progressNotification);
        }, isQuick ? 2000 : 3000);
    }

    completeExport(link, originalText, format, iframe, progressNotification) {
        // Reset button
        $(link).html(originalText);
        $(link).removeClass('disabled');
        this.exportInProgress = false;
        
        // Clean up iframe
        iframe.remove();
        
        // Update progress notification
        this.updateProgressNotification(progressNotification, format, 'success');
        
        // Auto-hide after delay
        setTimeout(() => {
            progressNotification.alert('close');
        }, 3000);
    }

    showProgressNotification(format) {
        const notification = $(`
            <div class="alert alert-info alert-dismissible fade show position-fixed export-progress" 
                 style="top: 20px; right: 20px; z-index: 1050; min-width: 350px;">
                <div class="d-flex align-items-center">
                    <div class="spinner-border spinner-border-sm me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>
                        <strong>Preparing ${format.toUpperCase()} Export</strong>
                        <div class="progress mt-1" style="height: 4px;">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('body').append(notification);
        
        // Animate progress bar
        setTimeout(() => {
            notification.find('.progress-bar').css('width', '100%');
        }, 100);
        
        return notification;
    }

    updateProgressNotification(notification, format, status) {
        const isSuccess = status === 'success';
        const alertClass = isSuccess ? 'alert-success' : 'alert-danger';
        const icon = isSuccess ? 'check-circle' : 'exclamation-circle';
        const message = isSuccess ? 
            `${format.toUpperCase()} export completed successfully!` : 
            `${format.toUpperCase()} export failed. Please try again.`;
        
        notification.removeClass('alert-info').addClass(alertClass);
        notification.find('.d-flex').html(`
            <i class="fas fa-${icon} me-2"></i>
            <strong>${message}</strong>
        `);
    }

    copyTableData() {
        const table = document.getElementById('reportTable');
        if (!table) {
            this.showNotification('No table data to copy', 'warning');
            return;
        }

        let csvContent = '';
        const rows = table.querySelectorAll('tr');

        rows.forEach(row => {
            const cells = row.querySelectorAll('th, td');
            const rowData = Array.from(cells).map(cell => {
                return '"' + cell.textContent.trim().replace(/"/g, '""') + '"';
            });
            csvContent += rowData.join(',') + '\n';
        });

        if (navigator.clipboard) {
            navigator.clipboard.writeText(csvContent).then(() => {
                this.showNotification('Table data copied to clipboard!', 'success');
            }).catch(() => {
                this.fallbackCopyTextToClipboard(csvContent);
            });
        } else {
            this.fallbackCopyTextToClipboard(csvContent);
        }
    }

    fallbackCopyTextToClipboard(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.top = "0";
        textArea.style.left = "0";
        textArea.style.position = "fixed";

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            if (successful) {
                this.showNotification('Table data copied to clipboard!', 'success');
            } else {
                this.showNotification('Failed to copy data', 'error');
            }
        } catch (err) {
            this.showNotification('Failed to copy data', 'error');
        }

        document.body.removeChild(textArea);
    }

    handlePrint(e) {
        const reportType = this.getReportType();
        const printWindow = window.open('', '_blank');
        const reportContent = $('.card').last().html();
        
        const dateRange = this.getDateRange();
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${reportType} - ${dateRange}</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                <style>
                    @media print {
                        .btn, .dropdown, .export-section { display: none !important; }
                        .card { border: none !important; box-shadow: none !important; }
                        body { font-size: 12px; }
                        table { font-size: 11px; }
                        .table td, .table th { padding: 0.25rem; }
                    }
                    .print-header { 
                        text-align: center; 
                        margin-bottom: 20px; 
                        border-bottom: 2px solid #333;
                        padding-bottom: 10px;
                    }
                    .print-date { 
                        text-align: right; 
                        margin-bottom: 10px; 
                        font-size: 0.9em;
                        color: #666;
                    }
                    .company-info {
                        text-align: center;
                        margin-bottom: 20px;
                        font-size: 0.9em;
                    }
                </style>
            </head>
            <body>
                <div class="company-info">
                    <strong>Csquare Technologies</strong><br>
                    Business Report System
                </div>
                <div class="print-header">
                    <h2>${reportType}</h2>
                    <p>${dateRange}</p>
                </div>
                <div class="print-date">
                    Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
                </div>
                ${reportContent}
                <div class="mt-4 text-center" style="font-size: 0.8em; color: #666;">
                    <p>This report was generated automatically by the ERP System</p>
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.focus();
        setTimeout(() => printWindow.print(), 500);
    }

    getReportType() {
        const title = $('h1').first().text().trim();
        return title || 'Report';
    }

    getDateRange() {
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();
        
        if (startDate && endDate) {
            return `Period: ${startDate} to ${endDate}`;
        }
        
        const badge = $('.badge').filter(':contains("Period:")').text();
        if (badge) {
            return badge;
        }
        
        return 'All Data';
    }

    showNotification(message, type = 'info') {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const icon = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        }[type] || 'info-circle';

        const notification = $(`
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 1050; min-width: 300px;">
                <i class="fas fa-${icon}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);

        $('body').append(notification);
        setTimeout(() => notification.alert('close'), 5000);
    }
}

// Initialize when document is ready
$(document).ready(function() {
    new ExportHelper();
});
