<?php
session_start();
require_once '../config/database.php';
require_once '../classes/Report.php';

$page_title = "Reports Dashboard";
$report = new Report($db);

// Get quick stats for dashboard
try {
    $stats = $report->getReportStats();
    $recent_invoices = $report->getInvoiceReport(date('Y-m-d', strtotime('-7 days')), date('Y-m-d'));
} catch (Exception $e) {
    $_SESSION['error'] = "Error loading dashboard: " . $e->getMessage();
    $stats = null;
    $recent_invoices = null;
}

include '../includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <h1 class="mb-4">
            <i class="fas fa-chart-bar"></i> Reports Dashboard
        </h1>
        <p class="text-muted">Generate and export comprehensive business reports</p>
    </div>
</div>

<!-- Quick Stats -->
<?php if ($stats): ?>
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-file-invoice fa-2x mb-2"></i>
                <h3><?php echo number_format($stats['total_invoices']); ?></h3>
                <p class="mb-0">Total Invoices</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                <h3>LKR <?php echo number_format($stats['total_revenue'], 2); ?></h3>
                <p class="mb-0">Total Revenue</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-calculator fa-2x mb-2"></i>
                <h3>LKR <?php echo number_format($stats['average_invoice'], 2); ?></h3>
                <p class="mb-0">Average Invoice</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-boxes fa-2x mb-2"></i>
                <h3><?php echo number_format($stats['total_items_sold']); ?></h3>
                <p class="mb-0">Items Sold</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Report Types -->
<div class="row mb-4">
    <div class="col-12">
        <h3 class="mb-3">
            <i class="fas fa-file-alt"></i> Available Reports
        </h3>
    </div>
</div>

<div class="row">
    <!-- Invoice Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 report-card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-file-invoice"></i> Invoice Report
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">
                    Generate detailed reports of all invoices with customer information, 
                    amounts, and date ranges. Perfect for financial analysis and customer insights.
                </p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Date range filtering</li>
                    <li><i class="fas fa-check text-success"></i> Customer filtering</li>
                    <li><i class="fas fa-check text-success"></i> Revenue analytics</li>
                    <li><i class="fas fa-check text-success"></i> CSV & PDF export</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="invoice_report.php" class="btn btn-primary btn-block">
                    <i class="fas fa-chart-line"></i> Generate Report
                </a>
                <div class="btn-group w-100 mt-2" role="group">
                    <a href="export_invoice_report.php?format=csv" 
                       class="btn btn-outline-success btn-sm quick-export" 
                       data-format="csv"
                       title="Quick CSV Export (Last 30 days)">
                        <i class="fas fa-file-csv"></i> Quick CSV
                    </a>
                    <a href="export_invoice_report.php?format=pdf" 
                       class="btn btn-outline-danger btn-sm quick-export" 
                       data-format="pdf"
                       title="Quick PDF Export (Last 30 days)">
                        <i class="fas fa-file-pdf"></i> Quick PDF
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Item Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 report-card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list-alt"></i> Invoice Item Report
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">
                    Detailed breakdown of individual items within invoices. 
                    Analyze product performance, pricing, and customer preferences.
                </p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Item-level details</li>
                    <li><i class="fas fa-check text-success"></i> Invoice filtering</li>
                    <li><i class="fas fa-check text-success"></i> Product analysis</li>
                    <li><i class="fas fa-check text-success"></i> CSV & PDF export</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="invoice_item_report.php" class="btn btn-info btn-block">
                    <i class="fas fa-search"></i> Generate Report
                </a>
                <div class="btn-group w-100 mt-2" role="group">
                    <a href="export_invoice_item_report.php?format=csv" 
                       class="btn btn-outline-success btn-sm quick-export" 
                       data-format="csv"
                       title="Quick CSV Export (Last 30 days)">
                        <i class="fas fa-file-csv"></i> Quick CSV
                    </a>
                    <a href="export_invoice_item_report.php?format=pdf" 
                       class="btn btn-outline-danger btn-sm quick-export" 
                       data-format="pdf"
                       title="Quick PDF Export (Last 30 days)">
                        <i class="fas fa-file-pdf"></i> Quick PDF
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Item Report -->
    <div class="col-lg-4 col-md-6 mb-4">
        <div class="card h-100 report-card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-boxes"></i> Item Inventory Report
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">
                    Complete inventory overview with stock levels, categories, 
                    and pricing information. Essential for inventory management.
                </p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Stock levels</li>
                    <li><i class="fas fa-check text-success"></i> Category filtering</li>
                    <li><i class="fas fa-check text-success"></i> Pricing overview</li>
                    <li><i class="fas fa-check text-success"></i> CSV & PDF export</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="item_report.php" class="btn btn-success btn-block">
                    <i class="fas fa-warehouse"></i> Generate Report
                </a>
                <div class="btn-group w-100 mt-2" role="group">
                    <a href="export_item_report.php?format=csv" 
                       class="btn btn-outline-success btn-sm quick-export" 
                       data-format="csv"
                       title="Quick CSV Export">
                        <i class="fas fa-file-csv"></i> Quick CSV
                    </a>
                    <a href="export_item_report.php?format=pdf" 
                       class="btn btn-outline-danger btn-sm quick-export" 
                       data-format="pdf"
                       title="Quick PDF Export">
                        <i class="fas fa-file-pdf"></i> Quick PDF
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<?php if ($recent_invoices && $recent_invoices->num_rows > 0): ?>
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-clock"></i> Recent Activity (Last 7 Days)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Invoice</th>
                                <th>Date</th>
                                <th>Customer</th>
                                <th>Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $count = 0;
                            while ($invoice = $recent_invoices->fetch_assoc() && $count < 5): 
                                $count++;
                            ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($invoice['invoice_no']); ?></strong></td>
                                    <td><?php echo htmlspecialchars($invoice['formatted_date']); ?></td>
                                    <td><?php echo htmlspecialchars($invoice['customer_name'] ?? 'N/A'); ?></td>
                                    <td><strong class="text-success">LKR <?php echo number_format($invoice['amount'], 2); ?></strong></td>
                                </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="invoice_report.php" class="btn btn-outline-primary">
                        <i class="fas fa-eye"></i> View All Invoices
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<style>
.report-card {
    transition: transform 0.2s;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-block {
    width: 100%;
}

.quick-export {
    position: relative;
}

.quick-export:hover {
    transform: scale(1.05);
}
</style>

<script>
$(document).ready(function() {
    // Enhanced quick export functionality
    $('.quick-export').on('click', function(e) {
        const link = this;
        const format = $(link).data('format');
        
        // Show loading state
        const originalText = $(link).html();
        $(link).html('<i class="fas fa-spinner fa-spin"></i>');
        $(link).addClass('disabled');
        
        // Create iframe for download
        const iframe = $('<iframe>').hide().appendTo('body');
        iframe.attr('src', $(link).attr('href'));
        
        // Reset after delay
        setTimeout(() => {
            $(link).html(originalText);
            $(link).removeClass('disabled');
            iframe.remove();
            
            // Show notification
            showNotification(`${format.toUpperCase()} export started!`, 'success');
        }, 2000);
        
        e.preventDefault();
        return false;
    });
    
    // Notification system
    function showNotification(message, type = 'info') {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const notification = $(`
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 1050; min-width: 300px;">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `);
        
        $('body').append(notification);
        setTimeout(() => notification.alert('close'), 4000);
    }
});
</script>

<?php include '../includes/footer.php'; ?>
