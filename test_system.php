<?php
/**
 * System Test Script
 * ERP System - Csquare Technologies
 * 
 * This script tests all core components of the ERP system
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>ERP System Component Test</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { background: #f8f9fa; }
        .test-container { background: white; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .test-section { padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
        .test-success { background: #d4edda; border-left-color: #28a745; }
        .test-error { background: #f8d7da; border-left-color: #dc3545; }
        .test-warning { background: #fff3cd; border-left-color: #ffc107; }
        .test-info { background: #d1ecf1; border-left-color: #17a2b8; }
    </style>
</head>
<body>
<div class='container py-4'>
    <div class='row'>
        <div class='col-12'>
            <div class='test-container p-4'>
                <div class='text-center mb-4'>
                    <h1><i class='fas fa-cogs'></i> ERP System Component Test</h1>
                    <p class='lead'>Testing all core components and functionality</p>
                </div>";

$test_results = [];

// Test 1: Database Connection
echo "<div class='test-section test-info'>
    <h4><i class='fas fa-database'></i> Test 1: Database Connection</h4>";

try {
    require_once 'config/database.php';
    echo "<p><i class='fas fa-check text-success'></i> Database connection successful</p>";
    echo "<p><i class='fas fa-info-circle'></i> Connected to database: " . DB_NAME . "</p>";
    $test_results['database'] = true;
} catch (Exception $e) {
    echo "<p><i class='fas fa-times text-danger'></i> Database connection failed: " . $e->getMessage() . "</p>";
    $test_results['database'] = false;
}

echo "</div>";

// Test 2: Customer Class
if ($test_results['database']) {
    echo "<div class='test-section test-info'>
        <h4><i class='fas fa-users'></i> Test 2: Customer Class</h4>";
    
    try {
        require_once 'classes/Customer.php';
        $customer = new Customer($db);
        
        // Test getting all customers
        $customers = $customer->getAllCustomers();
        $customer_count = $customers->num_rows;
        echo "<p><i class='fas fa-check text-success'></i> Customer class loaded successfully</p>";
        echo "<p><i class='fas fa-info-circle'></i> Found $customer_count customers in database</p>";
        
        // Test getting a specific customer
        if ($customer_count > 0) {
            $first_customer = $customer->getCustomerById(1);
            if ($first_customer) {
                echo "<p><i class='fas fa-check text-success'></i> Customer retrieval by ID working</p>";
                echo "<p><i class='fas fa-user'></i> Sample customer: " . $first_customer['title'] . " " . $first_customer['first_name'] . " " . $first_customer['last_name'] . "</p>";
            }
        }
        
        $test_results['customer'] = true;
    } catch (Exception $e) {
        echo "<p><i class='fas fa-times text-danger'></i> Customer class test failed: " . $e->getMessage() . "</p>";
        $test_results['customer'] = false;
    }
    
    echo "</div>";
}

// Test 3: Item Class
if ($test_results['database']) {
    echo "<div class='test-section test-info'>
        <h4><i class='fas fa-cube'></i> Test 3: Item Class</h4>";
    
    try {
        require_once 'classes/Item.php';
        $item = new Item($db);
        
        // Test getting all items
        $items = $item->getAllItems();
        $item_count = $items->num_rows;
        echo "<p><i class='fas fa-check text-success'></i> Item class loaded successfully</p>";
        echo "<p><i class='fas fa-info-circle'></i> Found $item_count items in database</p>";
        
        // Test getting categories
        $categories = $item->getCategories();
        $category_count = $categories->num_rows;
        echo "<p><i class='fas fa-check text-success'></i> Item categories loaded: $category_count categories</p>";
        
        // Test getting subcategories
        $subcategories = $item->getSubCategories();
        $subcategory_count = $subcategories->num_rows;
        echo "<p><i class='fas fa-check text-success'></i> Item subcategories loaded: $subcategory_count subcategories</p>";
        
        $test_results['item'] = true;
    } catch (Exception $e) {
        echo "<p><i class='fas fa-times text-danger'></i> Item class test failed: " . $e->getMessage() . "</p>";
        $test_results['item'] = false;
    }
    
    echo "</div>";
}

// Test 4: Report Class
if ($test_results['database']) {
    echo "<div class='test-section test-info'>
        <h4><i class='fas fa-chart-bar'></i> Test 4: Report Class</h4>";
    
    try {
        require_once 'classes/Report.php';
        $report = new Report($db);
        
        // Test invoice report
        $invoices = $report->getInvoiceReport();
        $invoice_count = $invoices->num_rows;
        echo "<p><i class='fas fa-check text-success'></i> Report class loaded successfully</p>";
        echo "<p><i class='fas fa-info-circle'></i> Found $invoice_count invoices for reporting</p>";
        
        // Test invoice item report
        $invoice_items = $report->getInvoiceItemReport();
        $invoice_item_count = $invoice_items->num_rows;
        echo "<p><i class='fas fa-check text-success'></i> Invoice item report working: $invoice_item_count items</p>";
        
        // Test item report
        $item_report = $report->getItemReport();
        $item_report_count = $item_report->num_rows;
        echo "<p><i class='fas fa-check text-success'></i> Item inventory report working: $item_report_count items</p>";
        
        $test_results['report'] = true;
    } catch (Exception $e) {
        echo "<p><i class='fas fa-times text-danger'></i> Report class test failed: " . $e->getMessage() . "</p>";
        $test_results['report'] = false;
    }
    
    echo "</div>";
}

// Test 5: PDF Generator
echo "<div class='test-section test-info'>
    <h4><i class='fas fa-file-pdf'></i> Test 5: PDF Generator</h4>";

try {
    require_once 'classes/PDFGenerator.php';
    $pdf = new PDFGenerator('Test Report');
    $pdf->setHeaders(['Column 1', 'Column 2', 'Column 3']);
    $pdf->addRow(['Test Data 1', 'Test Data 2', 'Test Data 3']);
    
    echo "<p><i class='fas fa-check text-success'></i> PDF Generator class loaded successfully</p>";
    echo "<p><i class='fas fa-info-circle'></i> PDF generation methods available</p>";
    
    $test_results['pdf'] = true;
} catch (Exception $e) {
    echo "<p><i class='fas fa-times text-danger'></i> PDF Generator test failed: " . $e->getMessage() . "</p>";
    $test_results['pdf'] = false;
}

echo "</div>";

// Test 6: File Structure
echo "<div class='test-section test-info'>
    <h4><i class='fas fa-folder-open'></i> Test 6: File Structure</h4>";

$required_files = [
    'index.php' => 'Main dashboard',
    'config/database.php' => 'Database configuration',
    'classes/Customer.php' => 'Customer management',
    'classes/Item.php' => 'Item management',
    'classes/Report.php' => 'Report generation',
    'classes/PDFGenerator.php' => 'PDF export functionality',
    'customer/index.php' => 'Customer listing',
    'item/index.php' => 'Item listing',
    'reports/invoice_report.php' => 'Invoice reports',
    'assets/css/style.css' => 'Custom styles',
    'database/assignment.sql' => 'Database schema'
];

$missing_files = [];
foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        echo "<p><i class='fas fa-check text-success'></i> $file - $description</p>";
    } else {
        echo "<p><i class='fas fa-times text-danger'></i> $file - $description (MISSING)</p>";
        $missing_files[] = $file;
    }
}

$test_results['files'] = empty($missing_files);

echo "</div>";

// Overall Test Results
$all_tests_passed = array_reduce($test_results, function($carry, $result) {
    return $carry && $result;
}, true);

echo "<div class='test-section " . ($all_tests_passed ? "test-success" : "test-warning") . "'>
    <h4><i class='fas fa-clipboard-check'></i> Overall Test Results</h4>";

if ($all_tests_passed) {
    echo "<p><i class='fas fa-check-circle text-success'></i> <strong>All tests passed!</strong> Your ERP system is ready to use.</p>
          <div class='mt-3'>
              <a href='index.php' class='btn btn-primary me-2'><i class='fas fa-home'></i> Go to Dashboard</a>
              <a href='setup_database.php' class='btn btn-info me-2'><i class='fas fa-database'></i> Database Setup</a>
              <a href='customer/' class='btn btn-success me-2'><i class='fas fa-users'></i> Manage Customers</a>
              <a href='item/' class='btn btn-warning'><i class='fas fa-cube'></i> Manage Items</a>
          </div>";
} else {
    echo "<p><i class='fas fa-exclamation-triangle text-warning'></i> <strong>Some tests failed.</strong> Please check the issues above.</p>";
    
    if (!$test_results['database']) {
        echo "<p><i class='fas fa-arrow-right'></i> Run the <a href='setup_database.php'>database setup script</a> first.</p>";
    }
    
    if (!empty($missing_files)) {
        echo "<p><i class='fas fa-arrow-right'></i> Missing files: " . implode(', ', $missing_files) . "</p>";
    }
    
    echo "<div class='mt-3'>
              <a href='setup_database.php' class='btn btn-primary me-2'><i class='fas fa-database'></i> Setup Database</a>
              <a href='test_system.php' class='btn btn-secondary'><i class='fas fa-redo'></i> Run Tests Again</a>
          </div>";
}

echo "</div>";

echo "            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
